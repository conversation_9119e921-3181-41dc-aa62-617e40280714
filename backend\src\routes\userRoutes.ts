import { Router } from 'express';
import { UserController } from '../controllers/UserController';
import { authMiddleware, requireRole } from '../middleware/authMiddleware';

const router = Router();

// All user routes require authentication
router.use(authMiddleware);

// Get all users (admin only)
router.get('/', requireRole(['admin']), UserController.getUsers);

// Get user statistics (admin only)
router.get('/stats', requireRole(['admin']), UserController.getUserStats);

// Get user by ID
router.get('/:id', UserController.getUserById);

// Create new user (admin only)
router.post('/', requireRole(['admin']), UserController.createUser);

// Update user (admin only)
router.put('/:id', requireRole(['admin']), UserController.updateUser);

// Delete user (admin only)
router.delete('/:id', requireRole(['admin']), UserController.deleteUser);

export { router as userRoutes };
