import { Router } from 'express';
import { AuthController } from '../controllers/AuthController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = Router();

// Public routes
router.post('/login', AuthController.login);
router.post('/register', AuthController.register);
router.post('/logout', AuthController.logout);

// Protected routes
router.get('/me', authMiddleware, AuthController.getCurrentUser);
router.post('/refresh', authMiddleware, AuthController.refreshToken);
router.post('/change-password', authMiddleware, AuthController.changePassword);

export { router as authRoutes };
