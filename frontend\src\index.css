@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-gray-50 text-gray-900;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-white;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 h-10 py-2 px-4;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 h-10 py-2 px-4;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-transparent hover:bg-gray-50 h-10 py-2 px-4;
  }
  
  .btn-ghost {
    @apply btn hover:bg-gray-100 hover:text-gray-900 h-10 py-2 px-4;
  }
  
  .btn-sm {
    @apply h-9 px-3 text-xs;
  }
  
  .btn-lg {
    @apply h-11 px-8;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-transparent px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }
  
  .card {
    @apply rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }
  
  .card-description {
    @apply text-sm text-gray-500;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .card-footer {
    @apply flex items-center p-6 pt-0;
  }
  
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .badge-default {
    @apply badge border-transparent bg-gray-900 text-gray-50 hover:bg-gray-900/80;
  }
  
  .badge-secondary {
    @apply badge border-transparent bg-gray-100 text-gray-900 hover:bg-gray-100/80;
  }
  
  .badge-success {
    @apply badge border-transparent bg-green-500 text-white hover:bg-green-500/80;
  }
  
  .badge-warning {
    @apply badge border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80;
  }
  
  .badge-destructive {
    @apply badge border-transparent bg-red-500 text-white hover:bg-red-500/80;
  }
  
  .badge-outline {
    @apply badge text-gray-950;
  }
}
