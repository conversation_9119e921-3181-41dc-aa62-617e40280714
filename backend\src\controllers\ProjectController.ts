import { Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { ProjectService } from '../services/ProjectService';
import { TenantRequest, ApiResponse } from '../types';

export class ProjectController {
  static async getProjects(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant) {
        res.status(400).json({
          success: false,
          error: 'Tenant context required',
        });
        return;
      }

      // Validation schema for query parameters
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1),
        limit: Joi.number().integer().min(1).max(100).default(10),
        search: Joi.string().optional(),
        sortBy: Joi.string().valid('createdAt', 'name', 'status', 'updatedAt').default('createdAt'),
        sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC'),
        status: Joi.string().valid('active', 'inactive', 'completed').optional(),
        ownerId: Joi.string().uuid().optional(),
      });

      const { error, value } = schema.validate(req.query);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
        return;
      }

      const result = await ProjectService.list(req.tenant.id, value);

      const response: ApiResponse = {
        success: true,
        data: result.projects,
        meta: {
          page: value.page,
          limit: value.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / value.limit),
        },
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async getProjectById(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant) {
        res.status(400).json({
          success: false,
          error: 'Tenant context required',
        });
        return;
      }

      const { id } = req.params;

      const project = await ProjectService.findById(id, req.tenant.id);

      if (!project) {
        res.status(404).json({
          success: false,
          error: 'Project not found',
        });
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: project.toJSON(),
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async createProject(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant || !req.user) {
        res.status(400).json({
          success: false,
          error: 'Tenant context and authentication required',
        });
        return;
      }

      // Validation schema
      const schema = Joi.object({
        name: Joi.string().min(1).max(100).required(),
        description: Joi.string().optional(),
        status: Joi.string().valid('active', 'inactive', 'completed').default('active'),
        settings: Joi.object().default({}),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
        return;
      }

      const projectData = {
        ...value,
        tenantId: req.tenant.id,
        ownerId: req.user.id,
      };

      const project = await ProjectService.create(projectData);

      const response: ApiResponse = {
        success: true,
        data: project.toJSON(),
        message: 'Project created successfully',
      };

      res.status(201).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async updateProject(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant) {
        res.status(400).json({
          success: false,
          error: 'Tenant context required',
        });
        return;
      }

      const { id } = req.params;

      // Validation schema
      const schema = Joi.object({
        name: Joi.string().min(1).max(100).optional(),
        description: Joi.string().optional(),
        status: Joi.string().valid('active', 'inactive', 'completed').optional(),
        settings: Joi.object().optional(),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
        return;
      }

      const project = await ProjectService.update(id, req.tenant.id, value);

      if (!project) {
        res.status(404).json({
          success: false,
          error: 'Project not found',
        });
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: project.toJSON(),
        message: 'Project updated successfully',
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async deleteProject(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant) {
        res.status(400).json({
          success: false,
          error: 'Tenant context required',
        });
        return;
      }

      const { id } = req.params;

      const success = await ProjectService.delete(id, req.tenant.id);

      if (!success) {
        res.status(404).json({
          success: false,
          error: 'Project not found',
        });
        return;
      }

      const response: ApiResponse = {
        success: true,
        message: 'Project deleted successfully',
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async getMyProjects(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant || !req.user) {
        res.status(400).json({
          success: false,
          error: 'Tenant context and authentication required',
        });
        return;
      }

      // Validation schema for query parameters
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1),
        limit: Joi.number().integer().min(1).max(100).default(10),
        search: Joi.string().optional(),
        sortBy: Joi.string().valid('createdAt', 'name', 'status', 'updatedAt').default('createdAt'),
        sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC'),
        status: Joi.string().valid('active', 'inactive', 'completed').optional(),
      });

      const { error, value } = schema.validate(req.query);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
        return;
      }

      const result = await ProjectService.getProjectsByOwner(req.user.id, req.tenant.id, value);

      const response: ApiResponse = {
        success: true,
        data: result.projects,
        meta: {
          page: value.page,
          limit: value.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / value.limit),
        },
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async getProjectStats(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant) {
        res.status(400).json({
          success: false,
          error: 'Tenant context required',
        });
        return;
      }

      const stats = await ProjectService.getProjectStats(req.tenant.id);

      const response: ApiResponse = {
        success: true,
        data: stats,
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async transferOwnership(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant) {
        res.status(400).json({
          success: false,
          error: 'Tenant context required',
        });
        return;
      }

      const { id } = req.params;

      // Validation schema
      const schema = Joi.object({
        newOwnerId: Joi.string().uuid().required(),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
        return;
      }

      const project = await ProjectService.transferOwnership(id, req.tenant.id, value.newOwnerId);

      if (!project) {
        res.status(404).json({
          success: false,
          error: 'Project not found',
        });
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: project.toJSON(),
        message: 'Project ownership transferred successfully',
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }
}
