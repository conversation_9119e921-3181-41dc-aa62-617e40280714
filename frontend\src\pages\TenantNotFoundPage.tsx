import React from 'react';
import { Link } from 'react-router-dom';
import { Building2, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/Button';

export const TenantNotFoundPage: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full text-center">
        <div className="flex justify-center mb-6">
          <Building2 className="w-16 h-16 text-gray-400" />
        </div>
        
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Tenant Not Found
        </h1>
        
        <p className="text-gray-600 mb-8">
          The organization you're trying to access doesn't exist or is no longer active. 
          Please check the URL or contact your administrator.
        </p>

        <div className="space-y-4">
          <div className="text-sm text-gray-500">
            <p>If you're trying to access a specific tenant, make sure you're using the correct:</p>
            <ul className="mt-2 space-y-1">
              <li>• Subdomain (e.g., yourcompany.app.com)</li>
              <li>• Tenant identifier in the URL</li>
            </ul>
          </div>

          <div className="pt-4">
            <Button
              onClick={() => window.location.href = '/'}
              className="w-full"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go to Homepage
            </Button>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-gray-200">
          <p className="text-xs text-gray-500">
            Need help? Contact support for assistance with accessing your organization.
          </p>
        </div>
      </div>
    </div>
  );
};
