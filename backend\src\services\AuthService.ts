import jwt, { SignOptions } from 'jsonwebtoken';
import { UserService } from './UserService';
import { TenantService } from './TenantService';
import { User } from '../models/User';
import { Tenant } from '../models/Tenant';
import { LoginRequest, LoginResponse } from '../types';

export class AuthService {
  static async login(loginData: LoginRequest): Promise<LoginResponse> {
    try {
      const { email, password, tenantId } = loginData;

      // Find tenant
      let tenant: Tenant | null = null;
      if (tenantId) {
        tenant = await TenantService.findByIdentifier(tenantId);
      }

      if (!tenant) {
        throw new Error('Tenant not found or inactive');
      }

      // Find user by email and tenant
      const user = await UserService.findByEmail(email, tenant.id);
      if (!user) {
        throw new Error('Invalid credentials');
      }

      // Verify password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        throw new Error('Invalid credentials');
      }

      // Update last login
      await UserService.updateLastLogin(user.id, tenant.id);

      // Generate JWT token
      const token = this.generateToken(user.id, tenant.id);

      // Return user data without password
      const userResponse = user.toJSON() as any;

      return {
        user: userResponse,
        token,
        tenant: tenant.toJSON() as any,
      };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  static async register(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    tenantId: string;
    role?: 'admin' | 'user';
  }): Promise<LoginResponse> {
    try {
      const { email, password, firstName, lastName, tenantId, role = 'user' } = userData;

      // Verify tenant exists
      const tenant = await TenantService.findById(tenantId);
      if (!tenant) {
        throw new Error('Tenant not found or inactive');
      }

      // Check if user already exists
      const existingUser = await UserService.findByEmail(email, tenantId);
      if (existingUser) {
        throw new Error('User already exists with this email');
      }

      // Create user
      const user = await UserService.create({
        email,
        password,
        firstName,
        lastName,
        tenantId,
        role,
        isActive: true,
      });

      // Generate JWT token
      const token = this.generateToken(user.id, tenantId);

      // Return user data without password
      const userResponse = user.toJSON() as any;

      return {
        user: userResponse,
        token,
        tenant: tenant.toJSON() as any,
      };
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  static async getCurrentUser(userId: string, tenantId: string): Promise<User | null> {
    try {
      return await UserService.findById(userId, tenantId);
    } catch (error) {
      console.error('Get current user error:', error);
      throw error;
    }
  }

  static async refreshToken(userId: string, tenantId: string): Promise<string> {
    try {
      // Verify user still exists and is active
      const user = await UserService.findById(userId, tenantId);
      if (!user) {
        throw new Error('User not found or inactive');
      }

      // Generate new token
      return this.generateToken(userId, tenantId);
    } catch (error) {
      console.error('Refresh token error:', error);
      throw error;
    }
  }

  static async changePassword(
    userId: string,
    tenantId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<boolean> {
    try {
      // Get user
      const user = await UserService.findById(userId, tenantId);
      if (!user) {
        throw new Error('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Update password
      return await UserService.changePassword(userId, tenantId, newPassword);
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  }

  private static generateToken(userId: string, tenantId: string): string {
    const payload = {
      userId,
      tenantId,
      iat: Math.floor(Date.now() / 1000),
    };

    return jwt.sign(payload, process.env.JWT_SECRET!, {
      expiresIn: '7d',
    });
  }

  static verifyToken(token: string): { userId: string; tenantId: string } {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      return {
        userId: decoded.userId,
        tenantId: decoded.tenantId,
      };
    } catch (error) {
      throw new Error('Invalid or expired token');
    }
  }
}
