import { Op } from 'sequelize';
import { User } from '../models/User';
import { Tenant } from '../models/Tenant';
import { UserAttributes, UserCreationAttributes, PaginationQuery } from '../types';

export class UserService {
  static async findById(id: string, tenantId: string): Promise<User | null> {
    try {
      return await User.findOne({
        where: {
          id,
          tenantId,
          isActive: true,
        },
        include: [
          {
            model: Tenant,
            as: 'tenant',
            attributes: ['id', 'name', 'subdomain'],
          },
        ],
      });
    } catch (error) {
      console.error('Error finding user by ID:', error);
      throw error;
    }
  }

  static async findByEmail(email: string, tenantId: string): Promise<User | null> {
    try {
      return await User.findOne({
        where: {
          email,
          tenantId,
          isActive: true,
        },
        include: [
          {
            model: Tenant,
            as: 'tenant',
            attributes: ['id', 'name', 'subdomain'],
          },
        ],
      });
    } catch (error) {
      console.error('Error finding user by email:', error);
      throw error;
    }
  }

  static async create(userData: UserCreationAttributes): Promise<User> {
    try {
      return await User.create(userData);
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  static async update(id: string, tenantId: string, updateData: Partial<UserAttributes>): Promise<User | null> {
    try {
      const [updatedRowsCount] = await User.update(updateData, {
        where: {
          id,
          tenantId,
        },
        returning: true,
      });

      if (updatedRowsCount === 0) {
        return null;
      }

      return await this.findById(id, tenantId);
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  static async delete(id: string, tenantId: string): Promise<boolean> {
    try {
      // Soft delete by setting isActive to false
      const [updatedRowsCount] = await User.update(
        { isActive: false },
        {
          where: {
            id,
            tenantId,
          },
        }
      );

      return updatedRowsCount > 0;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  static async list(
    tenantId: string,
    options: PaginationQuery = {}
  ): Promise<{ users: User[]; total: number }> {
    try {
      const { page = 1, limit = 10, search, sortBy = 'createdAt', sortOrder = 'DESC' } = options;
      const offset = (page - 1) * limit;

      const whereClause: any = {
        tenantId,
        isActive: true,
      };

      if (search) {
        whereClause[Op.or] = [
          { firstName: { [Op.iLike]: `%${search}%` } },
          { lastName: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } },
        ];
      }

      const { count, rows } = await User.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        order: [[sortBy, sortOrder]],
        include: [
          {
            model: Tenant,
            as: 'tenant',
            attributes: ['id', 'name', 'subdomain'],
          },
        ],
      });

      return {
        users: rows,
        total: count,
      };
    } catch (error) {
      console.error('Error listing users:', error);
      throw error;
    }
  }

  static async updateLastLogin(id: string, tenantId: string): Promise<void> {
    try {
      await User.update(
        { lastLoginAt: new Date() },
        {
          where: {
            id,
            tenantId,
          },
        }
      );
    } catch (error) {
      console.error('Error updating last login:', error);
      throw error;
    }
  }

  static async changePassword(id: string, tenantId: string, newPassword: string): Promise<boolean> {
    try {
      const [updatedRowsCount] = await User.update(
        { password: newPassword },
        {
          where: {
            id,
            tenantId,
          },
        }
      );

      return updatedRowsCount > 0;
    } catch (error) {
      console.error('Error changing password:', error);
      throw error;
    }
  }

  static async getUserStats(tenantId: string): Promise<{
    total: number;
    active: number;
    admins: number;
    recentLogins: number;
  }> {
    try {
      const total = await User.count({
        where: { tenantId },
      });

      const active = await User.count({
        where: {
          tenantId,
          isActive: true,
        },
      });

      const admins = await User.count({
        where: {
          tenantId,
          role: 'admin',
          isActive: true,
        },
      });

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentLogins = await User.count({
        where: {
          tenantId,
          isActive: true,
          lastLoginAt: {
            [Op.gte]: thirtyDaysAgo,
          },
        },
      });

      return {
        total,
        active,
        admins,
        recentLogins,
      };
    } catch (error) {
      console.error('Error getting user stats:', error);
      throw error;
    }
  }
}
