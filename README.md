# Multi-Tenant Web Application Starter

A comprehensive starter project for building multi-tenant web applications with Node.js/Express backend and React frontend.

## Architecture Overview

### Backend (Node.js + Express + TypeScript)
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Sequelize ORM
- **Multi-tenancy**: Subdomain-based tenant identification with shared database schema
- **Architecture**: Clean code architecture (routes, controllers, services, models)
- **Authentication**: JWT-based auth with tenant context

### Frontend (React + TypeScript)
- **Framework**: React with TypeScript (Vite)
- **State Management**: React Query for server state
- **HTTP Client**: Axios
- **Routing**: React Router with multi-tenant support

## Project Structure

```
├── backend/                 # Node.js/Express API
│   ├── src/
│   │   ├── config/         # Configuration files
│   │   ├── controllers/    # Request handlers
│   │   ├── middleware/     # Custom middleware
│   │   ├── models/         # Database models
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   └── types/          # TypeScript type definitions
│   ├── Dockerfile
│   └── package.json
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── hooks/          # Custom hooks
│   │   └── types/          # TypeScript interfaces
│   ├── Dockerfile
│   └── package.json
├── docker-compose.yml      # Multi-service Docker setup
└── README.md
```

## Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Docker & Docker Compose (recommended)

### Using Docker (Recommended)

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd QA_Anys
   ```

2. Copy environment files:
   ```bash
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env
   ```

3. Start all services:
   ```bash
   # Development mode
   docker-compose up -d

   # Production mode
   docker-compose -f docker-compose.prod.yml up -d
   ```

4. Access the application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - Database: localhost:5432

### Manual Setup

#### Backend Setup
```bash
cd backend
npm install
cp .env.example .env
# Configure your database in .env
npm run dev
```

#### Frontend Setup
```bash
cd frontend
npm install
cp .env.example .env
npm run dev
```

### Initial Setup

After starting the services, the database will be automatically initialized with:
- Default tenant (subdomain: `default`)
- Demo tenant (subdomain: `demo`)
- Admin user: `<EMAIL>` / `admin123`
- Demo user: `<EMAIL>` / `demo123`

## Multi-Tenancy

This application supports multi-tenancy through:

1. **Subdomain-based tenant identification**: `tenant1.localhost:3000`
2. **Fallback header-based**: `X-Tenant-ID` header
3. **Shared database with tenant isolation**: All tables include `tenant_id` column
4. **Automatic tenant context**: Sequelize automatically filters by tenant

## API Endpoints

### Authentication
- `POST /api/auth/login` - Login with email/password
- `POST /api/auth/logout` - Logout
- `GET /api/auth/me` - Get current user

### Users
- `GET /api/users` - List users (tenant-scoped)
- `GET /api/users/:id` - Get user details

### Projects
- `GET /api/projects` - List projects (tenant-scoped)
- `POST /api/projects` - Create project
- `PUT /api/projects/:id` - Update project
- `DELETE /api/projects/:id` - Delete project

## Environment Variables

See `.env.example` files in both backend and frontend directories for required configuration.

## Development

### Local Development URLs
- Frontend: `http://localhost:3000`
- Backend API: `http://localhost:3001`
- Database: `localhost:5432`

### Testing Multi-tenancy

#### Method 1: Subdomain-based (Recommended for Production)
1. Add entries to your hosts file (`/etc/hosts` on Unix, `C:\Windows\System32\drivers\etc\hosts` on Windows):
   ```
   127.0.0.1 default.localhost
   127.0.0.1 demo.localhost
   ```

2. Access different tenants:
   - Default tenant: `http://default.localhost:3000`
   - Demo tenant: `http://demo.localhost:3000`

#### Method 2: Header-based (Development Fallback)
Use the `X-Tenant-ID` header in your API requests:
```bash
curl -H "X-Tenant-ID: default" http://localhost:3001/api/projects
```

### Development Commands

#### Backend
```bash
cd backend
npm run dev          # Start development server
npm run build        # Build for production
npm run lint         # Run ESLint
npm run test         # Run tests
npm run db:migrate   # Run database migrations
npm run db:seed      # Seed database with sample data
```

#### Frontend
```bash
cd frontend
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
```

## Deployment

### Production Deployment with Docker

1. Create production environment file:
   ```bash
   cp .env.example .env.production
   # Edit .env.production with your production values
   ```

2. Deploy with Docker Compose:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

### Environment Variables

#### Backend (.env)
```bash
NODE_ENV=production
DATABASE_URL=************************************/database
JWT_SECRET=your-super-secret-jwt-key
CORS_ORIGIN=https://yourdomain.com
```

#### Frontend (.env)
```bash
VITE_API_URL=https://api.yourdomain.com
VITE_DEFAULT_TENANT=default
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure PostgreSQL is running
   - Check DATABASE_URL in backend/.env
   - Verify database credentials

2. **CORS Issues**
   - Check CORS_ORIGIN in backend/.env
   - Ensure frontend URL matches CORS configuration

3. **Tenant Not Found**
   - Verify tenant exists in database
   - Check subdomain configuration
   - Ensure X-Tenant-ID header is set correctly

4. **Authentication Issues**
   - Check JWT_SECRET configuration
   - Verify token expiration settings
   - Clear browser cookies and localStorage

### Docker Issues

1. **Port Already in Use**
   ```bash
   docker-compose down
   # Change ports in docker-compose.yml if needed
   docker-compose up -d
   ```

2. **Database Initialization**
   ```bash
   docker-compose down -v  # Remove volumes
   docker-compose up -d    # Recreate with fresh database
   ```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and add tests
4. Run linting and tests: `npm run lint && npm run test`
5. Commit your changes: `git commit -am 'Add feature'`
6. Push to the branch: `git push origin feature-name`
7. Create a Pull Request

## License

MIT License - see LICENSE file for details
