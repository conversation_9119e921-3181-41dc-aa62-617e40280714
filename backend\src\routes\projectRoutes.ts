import { Router } from 'express';
import { ProjectController } from '../controllers/ProjectController';
import { authMiddleware, requireRole } from '../middleware/authMiddleware';

const router = Router();

// All project routes require authentication
router.use(authMiddleware);

// Get all projects
router.get('/', ProjectController.getProjects);

// Get project statistics
router.get('/stats', ProjectController.getProjectStats);

// Get current user's projects
router.get('/my-projects', ProjectController.getMyProjects);

// Get project by ID
router.get('/:id', ProjectController.getProjectById);

// Create new project
router.post('/', ProjectController.createProject);

// Update project
router.put('/:id', ProjectController.updateProject);

// Delete project (admin only or project owner)
router.delete('/:id', ProjectController.deleteProject);

// Transfer project ownership (admin only)
router.post('/:id/transfer', requireRole(['admin']), ProjectController.transferOwnership);

export { router as projectRoutes };
