# Production Environment Configuration
# Copy this file to .env.production and update with your production values

# Database Configuration
DATABASE_URL=****************************************/database_name
POSTGRES_PASSWORD=your_secure_database_password

# Backend Configuration
NODE_ENV=production
JWT_SECRET=your-super-secure-jwt-secret-key-minimum-32-characters-long
PORT=3001

# CORS Configuration
CORS_ORIGIN=https://yourdomain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# Multi-tenant Configuration
DEFAULT_TENANT=default
TENANT_HEADER_NAME=X-Tenant-ID

# Frontend Configuration
VITE_API_URL=https://api.yourdomain.com
VITE_APP_NAME=Your Multi-Tenant App
VITE_DEFAULT_TENANT=default
VITE_TENANT_HEADER_NAME=X-Tenant-ID

# URLs for Docker Compose
FRONTEND_URL=https://yourdomain.com
API_URL=https://api.yourdomain.com

# SSL Configuration (if using custom certificates)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Monitoring (Optional)
SENTRY_DSN=your_sentry_dsn_here
NEW_RELIC_LICENSE_KEY=your_new_relic_key_here

# Email Configuration (Future enhancement)
SMTP_HOST=smtp.yourmailprovider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# Storage Configuration (Future enhancement)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name
