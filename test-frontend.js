// Simple test to verify frontend tenant resolution
console.log('Testing frontend tenant resolution...');

// Simulate different URL scenarios
const testCases = [
  { url: 'http://localhost:3000/', expected: 'lshack' },
  { url: 'http://localhost:3000/login', expected: 'lshack' },
  { url: 'http://localhost:3000/dashboard', expected: 'lshack' },
  { url: 'http://localhost:3000/projects', expected: 'lshack' },
  { url: 'http://demo.localhost:3000/', expected: 'demo' },
  { url: 'http://acme.localhost:3000/', expected: 'acme' },
];

// Mock window.location for testing
function mockLocation(url) {
  const urlObj = new URL(url);
  return {
    hostname: urlObj.hostname,
    pathname: urlObj.pathname,
    port: urlObj.port
  };
}

// Mock import.meta.env
const mockEnv = {
  VITE_DEFAULT_TENANT: 'lshack'
};

// Tenant utility functions (copied from frontend)
function getTenantFromSubdomain(location) {
  const hostname = location.hostname;
  
  if (hostname === 'localhost' || hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
    return null;
  }
  
  const parts = hostname.split('.');
  if (parts.length > 2) {
    return parts[0];
  }
  
  return null;
}

function getTenantFromPath(location) {
  const pathname = location.pathname;
  const pathParts = pathname.split('/').filter(Boolean);
  
  const excludedPaths = [
    'login', 'register', 'logout', 'dashboard', 'projects', 'users', 'profile',
    'settings', 'admin', 'api', 'health', 'tenant-not-found', 'error'
  ];
  
  if (pathParts.length > 0 && 
      pathParts[0].match(/^[a-z0-9-]+$/) && 
      !excludedPaths.includes(pathParts[0])) {
    return pathParts[0];
  }
  
  return null;
}

function getCurrentTenant(location) {
  if (location.hostname === 'localhost' || location.hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
    return mockEnv.VITE_DEFAULT_TENANT || 'lshack';
  }

  const subdomainTenant = getTenantFromSubdomain(location);
  if (subdomainTenant) {
    return subdomainTenant;
  }

  const pathTenant = getTenantFromPath(location);
  if (pathTenant) {
    return pathTenant;
  }

  return mockEnv.VITE_DEFAULT_TENANT || 'lshack';
}

// Run tests
console.log('\n=== FRONTEND TENANT RESOLUTION TESTS ===\n');

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  const location = mockLocation(testCase.url);
  const result = getCurrentTenant(location);
  const success = result === testCase.expected;
  
  console.log(`Test ${index + 1}: ${testCase.url}`);
  console.log(`  Expected: ${testCase.expected}`);
  console.log(`  Got: ${result}`);
  console.log(`  Status: ${success ? '✅ PASS' : '❌ FAIL'}`);
  console.log('');
  
  if (success) {
    passed++;
  } else {
    failed++;
  }
});

console.log(`=== RESULTS ===`);
console.log(`✅ Passed: ${passed}`);
console.log(`❌ Failed: ${failed}`);
console.log(`📊 Total: ${testCases.length}`);

if (failed === 0) {
  console.log('\n🎉 All tests passed! Frontend tenant resolution is working correctly.');
} else {
  console.log('\n⚠️  Some tests failed. Please check the tenant resolution logic.');
}
