import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { ProjectAttributes, ProjectCreationAttributes } from '../types';

class Project extends Model<ProjectAttributes, ProjectCreationAttributes> implements ProjectAttributes {
  public id!: string;
  public name!: string;
  public description!: string;
  public status!: 'active' | 'inactive' | 'completed';
  public tenantId!: string;
  public ownerId!: string;
  public settings!: Record<string, any>;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Associations
  static associate(models: any) {
    // A project belongs to a tenant
    Project.belongsTo(models.Tenant, {
      foreignKey: 'tenantId',
      as: 'tenant',
    });

    // A project belongs to a user (owner)
    Project.belongsTo(models.User, {
      foreignKey: 'ownerId',
      as: 'owner',
    });
  }
}

Project.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 100],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'completed'),
      defaultValue: 'active',
    },
    tenantId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    ownerId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    settings: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: 'Project',
    tableName: 'projects',
    timestamps: true,
    indexes: [
      {
        fields: ['tenantId'],
      },
      {
        fields: ['ownerId'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['tenantId', 'status'],
      },
    ],
  }
);

export { Project };
