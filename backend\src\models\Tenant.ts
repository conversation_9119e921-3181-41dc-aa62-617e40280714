import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { TenantAttributes, TenantCreationAttributes } from '../types';

class Tenant extends Model<TenantAttributes, TenantCreationAttributes> implements TenantAttributes {
  public id!: string;
  public name!: string;
  public subdomain!: string;
  public isActive!: boolean;
  public settings!: Record<string, any>;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Associations
  static associate(models: any) {
    // A tenant has many users
    Tenant.hasMany(models.User, {
      foreignKey: 'tenantId',
      as: 'users',
    });

    // A tenant has many projects
    Tenant.hasMany(models.Project, {
      foreignKey: 'tenantId',
      as: 'projects',
    });
  }
}

Tenant.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100],
      },
    },
    subdomain: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        len: [2, 50],
        is: /^[a-z0-9-]+$/i, // Only alphanumeric and hyphens
      },
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    settings: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: 'Tenant',
    tableName: 'tenants',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['subdomain'],
      },
      {
        fields: ['isActive'],
      },
    ],
  }
);

export { Tenant };
