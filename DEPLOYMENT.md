# Deployment Guide

This guide covers deploying the Multi-Tenant Web Application to various environments.

## Table of Contents
- [Prerequisites](#prerequisites)
- [Environment Configuration](#environment-configuration)
- [Docker Deployment](#docker-deployment)
- [Cloud Deployment](#cloud-deployment)
- [Database Setup](#database-setup)
- [SSL/TLS Configuration](#ssltls-configuration)
- [Monitoring and Logging](#monitoring-and-logging)

## Prerequisites

- Docker & Docker Compose
- PostgreSQL database
- Domain name (for production)
- SSL certificates (for HTTPS)

## Environment Configuration

### Production Environment Variables

Create a `.env.production` file:

```bash
# Database
DATABASE_URL=****************************************/database_name
POSTGRES_PASSWORD=secure_password

# Backend
NODE_ENV=production
JWT_SECRET=your-super-secure-jwt-secret-key-min-32-chars
CORS_ORIGIN=https://yourdomain.com
PORT=3001

# Frontend
VITE_API_URL=https://api.yourdomain.com
FRONTEND_URL=https://yourdomain.com
API_URL=https://api.yourdomain.com

# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## Docker Deployment

### Production Deployment

1. **Build and start services:**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

2. **Check service status:**
   ```bash
   docker-compose -f docker-compose.prod.yml ps
   ```

3. **View logs:**
   ```bash
   docker-compose -f docker-compose.prod.yml logs -f
   ```

### Scaling Services

Scale specific services:
```bash
docker-compose -f docker-compose.prod.yml up -d --scale backend=3
```

## Cloud Deployment

### AWS Deployment

#### Using AWS ECS

1. **Create ECR repositories:**
   ```bash
   aws ecr create-repository --repository-name multitenant-backend
   aws ecr create-repository --repository-name multitenant-frontend
   ```

2. **Build and push images:**
   ```bash
   # Backend
   docker build -t multitenant-backend ./backend
   docker tag multitenant-backend:latest <account-id>.dkr.ecr.<region>.amazonaws.com/multitenant-backend:latest
   docker push <account-id>.dkr.ecr.<region>.amazonaws.com/multitenant-backend:latest

   # Frontend
   docker build -t multitenant-frontend ./frontend
   docker tag multitenant-frontend:latest <account-id>.dkr.ecr.<region>.amazonaws.com/multitenant-frontend:latest
   docker push <account-id>.dkr.ecr.<region>.amazonaws.com/multitenant-frontend:latest
   ```

3. **Create ECS task definitions and services**

#### Using AWS RDS for Database

1. **Create RDS PostgreSQL instance**
2. **Update DATABASE_URL in environment variables**
3. **Configure security groups for database access**

### Google Cloud Platform

#### Using Cloud Run

1. **Build and push to Container Registry:**
   ```bash
   # Backend
   gcloud builds submit --tag gcr.io/PROJECT_ID/multitenant-backend ./backend
   
   # Frontend
   gcloud builds submit --tag gcr.io/PROJECT_ID/multitenant-frontend ./frontend
   ```

2. **Deploy to Cloud Run:**
   ```bash
   gcloud run deploy multitenant-backend \
     --image gcr.io/PROJECT_ID/multitenant-backend \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated

   gcloud run deploy multitenant-frontend \
     --image gcr.io/PROJECT_ID/multitenant-frontend \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated
   ```

### DigitalOcean App Platform

1. **Create app specification file (`app.yaml`):**
   ```yaml
   name: multitenant-app
   services:
   - name: backend
     source_dir: /backend
     github:
       repo: your-username/your-repo
       branch: main
     run_command: npm start
     environment_slug: node-js
     instance_count: 1
     instance_size_slug: basic-xxs
     envs:
     - key: NODE_ENV
       value: production
     - key: DATABASE_URL
       value: ${db.DATABASE_URL}
     - key: JWT_SECRET
       value: ${JWT_SECRET}
   
   - name: frontend
     source_dir: /frontend
     github:
       repo: your-username/your-repo
       branch: main
     build_command: npm run build
     run_command: npm run preview
     environment_slug: node-js
     instance_count: 1
     instance_size_slug: basic-xxs
   
   databases:
   - name: db
     engine: PG
     version: "13"
   ```

2. **Deploy:**
   ```bash
   doctl apps create --spec app.yaml
   ```

## Database Setup

### Production Database Configuration

1. **Create database and user:**
   ```sql
   CREATE DATABASE multitenant_app;
   CREATE USER app_user WITH PASSWORD 'secure_password';
   GRANT ALL PRIVILEGES ON DATABASE multitenant_app TO app_user;
   ```

2. **Run migrations:**
   ```bash
   docker-compose -f docker-compose.prod.yml exec backend npm run db:migrate
   ```

3. **Seed initial data:**
   ```bash
   docker-compose -f docker-compose.prod.yml exec backend npm run db:seed
   ```

### Database Backup

Create automated backups:
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump $DATABASE_URL > backup_$DATE.sql
```

## SSL/TLS Configuration

### Using Let's Encrypt with Certbot

1. **Install Certbot:**
   ```bash
   sudo apt-get install certbot python3-certbot-nginx
   ```

2. **Obtain certificates:**
   ```bash
   sudo certbot --nginx -d yourdomain.com -d *.yourdomain.com
   ```

3. **Update nginx configuration for HTTPS**

### Using Custom SSL Certificates

1. **Place certificates in `ssl/` directory:**
   ```
   ssl/
   ├── cert.pem
   └── key.pem
   ```

2. **Update nginx configuration to use SSL**

## Monitoring and Logging

### Health Checks

The application includes health check endpoints:
- Backend: `GET /health`
- Database connectivity is verified

### Logging

Configure centralized logging:

1. **Using ELK Stack:**
   ```yaml
   # Add to docker-compose.prod.yml
   elasticsearch:
     image: elasticsearch:7.14.0
   
   logstash:
     image: logstash:7.14.0
   
   kibana:
     image: kibana:7.14.0
   ```

2. **Using external services:**
   - AWS CloudWatch
   - Google Cloud Logging
   - Datadog
   - New Relic

### Monitoring

Set up monitoring for:
- Application uptime
- Database performance
- API response times
- Error rates
- Resource usage

## Security Considerations

1. **Environment Variables:**
   - Never commit secrets to version control
   - Use secure secret management services

2. **Database Security:**
   - Use strong passwords
   - Enable SSL connections
   - Restrict network access

3. **Application Security:**
   - Keep dependencies updated
   - Use HTTPS in production
   - Implement rate limiting
   - Regular security audits

## Rollback Strategy

1. **Tag releases:**
   ```bash
   git tag -a v1.0.0 -m "Release version 1.0.0"
   ```

2. **Keep previous images:**
   ```bash
   docker tag current-image:latest previous-image:v1.0.0
   ```

3. **Quick rollback:**
   ```bash
   docker-compose -f docker-compose.prod.yml down
   # Update image tags to previous version
   docker-compose -f docker-compose.prod.yml up -d
   ```

## Performance Optimization

1. **Database optimization:**
   - Add appropriate indexes
   - Configure connection pooling
   - Monitor query performance

2. **Application optimization:**
   - Enable gzip compression
   - Use CDN for static assets
   - Implement caching strategies

3. **Infrastructure optimization:**
   - Use load balancers
   - Auto-scaling groups
   - Content delivery networks
