const { Sequelize, DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');

// Create database connection
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: './database.sqlite',
  logging: false,
});

// Define models
const Tenant = sequelize.define('Tenant', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  subdomain: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  },
  settings: {
    type: DataTypes.JSON,
    defaultValue: {},
  },
}, {
  tableName: 'tenants',
  timestamps: true,
});

const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  firstName: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  lastName: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  role: {
    type: DataTypes.ENUM('admin', 'user'),
    defaultValue: 'user',
  },
  tenantId: {
    type: DataTypes.UUID,
    allowNull: false,
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  },
  lastLoginAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
}, {
  tableName: 'users',
  timestamps: true,
});

async function addLsHackTenant() {
  try {
    console.log('🔧 Adding LsHack tenant...');

    // Check if LsHack tenant already exists
    const existingTenant = await Tenant.findOne({
      where: { subdomain: 'lshack' }
    });

    if (existingTenant) {
      console.log('✅ LsHack tenant already exists');
      console.log('Tenant ID:', existingTenant.id);
      console.log('Name:', existingTenant.name);
      console.log('Subdomain:', existingTenant.subdomain);
      return;
    }

    // Create LsHack tenant
    const lshackTenant = await Tenant.create({
      name: 'LsHack Development',
      subdomain: 'lshack',
      isActive: true,
      settings: { 
        theme: 'dark', 
        features: ['projects', 'users', 'analytics', 'no-auth'],
        authBypass: true 
      },
    });
    console.log('✅ Created LsHack tenant');

    // Hash password for demo user
    const userPassword = await bcrypt.hash('lshack123', 12);

    // Create a demo user for LsHack tenant
    const lshackUser = await User.create({
      email: '<EMAIL>',
      password: userPassword,
      firstName: 'LsHack',
      lastName: 'Admin',
      role: 'admin',
      tenantId: lshackTenant.id,
      isActive: true,
    });
    console.log('✅ Created admin user for LsHack tenant');

    console.log('\n🎉 LsHack tenant setup completed!');
    console.log('\n📊 LsHack Tenant Details:');
    console.log('Tenant ID:', lshackTenant.id);
    console.log('Name:', lshackTenant.name);
    console.log('Subdomain:', lshackTenant.subdomain);
    console.log('Settings:', JSON.stringify(lshackTenant.settings, null, 2));
    
    console.log('\n👤 LsHack User Details:');
    console.log('Email: <EMAIL>');
    console.log('Password: lshack123');
    console.log('Role: admin');
    
    console.log('\n🌐 Access URLs:');
    console.log('With Header: curl -H "X-Tenant-ID: lshack" http://localhost:3001/health');
    console.log('Frontend: http://localhost:3000 (set tenant to lshack)');
    
  } catch (error) {
    console.error('❌ Error adding LsHack tenant:', error);
  } finally {
    await sequelize.close();
  }
}

addLsHackTenant();
