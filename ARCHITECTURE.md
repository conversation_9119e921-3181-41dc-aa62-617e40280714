# Architecture Documentation

This document describes the architecture and design decisions of the Multi-Tenant Web Application.

## Table of Contents
- [Overview](#overview)
- [System Architecture](#system-architecture)
- [Multi-Tenancy Strategy](#multi-tenancy-strategy)
- [Backend Architecture](#backend-architecture)
- [Frontend Architecture](#frontend-architecture)
- [Database Design](#database-design)
- [Security Architecture](#security-architecture)
- [API Design](#api-design)

## Overview

The Multi-Tenant Web Application is designed as a modern, scalable SaaS platform that supports multiple organizations (tenants) within a single application instance. The architecture follows clean code principles and implements a shared database with tenant isolation strategy.

### Key Features
- **Multi-tenancy**: Subdomain and header-based tenant identification
- **Clean Architecture**: Separation of concerns with clear boundaries
- **Type Safety**: Full TypeScript implementation
- **Real-time Updates**: React Query for efficient data synchronization
- **Scalability**: Containerized deployment with Docker
- **Security**: JWT authentication with tenant context

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │     CDN         │    │   Monitoring    │
│   (Nginx)       │    │                 │    │   (Optional)    │
└─────────┬───────┘    └─────────────────┘    └─────────────────┘
          │
          ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│   (React/Vite)  │◄──►│   (Node.js)     │◄──►│   (PostgreSQL)  │
│   Port: 3000    │    │   Port: 3001    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack

#### Backend
- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: PostgreSQL with Sequelize ORM
- **Authentication**: JWT with bcrypt
- **Validation**: Joi
- **Security**: Helmet, CORS, Rate Limiting

#### Frontend
- **Framework**: React 18
- **Build Tool**: Vite
- **Language**: TypeScript
- **Routing**: React Router v6
- **State Management**: React Query (TanStack Query)
- **HTTP Client**: Axios
- **Styling**: Tailwind CSS
- **Forms**: React Hook Form with Zod validation

#### Infrastructure
- **Containerization**: Docker & Docker Compose
- **Reverse Proxy**: Nginx
- **Database**: PostgreSQL 15

## Multi-Tenancy Strategy

### Tenant Identification

The application supports multiple methods for tenant identification:

1. **Subdomain-based (Primary)**
   ```
   tenant1.yourdomain.com → Tenant: tenant1
   tenant2.yourdomain.com → Tenant: tenant2
   ```

2. **Header-based (Fallback)**
   ```
   X-Tenant-ID: tenant1
   ```

3. **Path-based (Development)**
   ```
   yourdomain.com/tenant1 → Tenant: tenant1
   ```

### Data Isolation Strategy

**Shared Database with Row-Level Security**
- Single database instance
- All tables include `tenant_id` column
- Automatic filtering by tenant context
- Cost-effective and easier to maintain

```sql
-- Example table structure
CREATE TABLE projects (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    -- other columns
);

-- Automatic tenant filtering in queries
SELECT * FROM projects WHERE tenant_id = $1;
```

### Benefits
- **Cost Efficiency**: Single database instance
- **Easier Maintenance**: Centralized updates and backups
- **Resource Sharing**: Efficient resource utilization
- **Scalability**: Horizontal scaling with load balancers

### Trade-offs
- **Security Risk**: Potential data leakage if tenant filtering fails
- **Performance**: Shared resources may impact performance
- **Customization**: Limited per-tenant customization

## Backend Architecture

### Clean Architecture Layers

```
┌─────────────────────────────────────────┐
│                Routes                   │ ← HTTP Layer
├─────────────────────────────────────────┤
│              Controllers                │ ← Presentation Layer
├─────────────────────────────────────────┤
│               Services                  │ ← Business Logic Layer
├─────────────────────────────────────────┤
│                Models                   │ ← Data Access Layer
├─────────────────────────────────────────┤
│               Database                  │ ← Infrastructure Layer
└─────────────────────────────────────────┘
```

### Directory Structure
```
backend/src/
├── config/          # Configuration files
├── controllers/     # Request handlers
├── middleware/      # Custom middleware
├── models/          # Database models
├── routes/          # API routes
├── services/        # Business logic
├── types/           # TypeScript definitions
└── utils/           # Utility functions
```

### Key Components

#### Middleware
- **tenantMiddleware**: Extracts and validates tenant context
- **authMiddleware**: JWT authentication and user context
- **errorHandler**: Centralized error handling
- **rateLimiter**: API rate limiting

#### Services
- **AuthService**: Authentication and authorization
- **UserService**: User management with tenant isolation
- **ProjectService**: Project CRUD operations
- **TenantService**: Tenant management

#### Models
- **Tenant**: Organization/tenant entity
- **User**: User entity with tenant association
- **Project**: Project entity with tenant and owner association

## Frontend Architecture

### Component Architecture

```
src/
├── components/      # Reusable UI components
│   ├── ui/         # Basic UI components
│   └── Layout.tsx  # Application layout
├── contexts/       # React contexts
├── hooks/          # Custom React hooks
├── pages/          # Page components
├── services/       # API services
├── types/          # TypeScript definitions
└── utils/          # Utility functions
```

### State Management Strategy

#### Server State (React Query)
- API data caching and synchronization
- Background refetching
- Optimistic updates
- Error handling

#### Client State (React Context)
- Authentication state
- Tenant context
- UI state

#### Local State (useState/useReducer)
- Component-specific state
- Form state (React Hook Form)

### Key Features

#### Multi-tenant Routing
```typescript
// Tenant detection utility
export const getCurrentTenant = (): string | null => {
  // 1. Check subdomain
  // 2. Check path
  // 3. Fallback to default
};
```

#### Authentication Flow
```typescript
// Auth context with tenant awareness
const AuthContext = createContext<{
  user: User | null;
  tenant: Tenant | null;
  login: (credentials) => Promise<void>;
  logout: () => void;
}>();
```

## Database Design

### Entity Relationship Diagram

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   Tenants   │     │    Users    │     │  Projects   │
├─────────────┤     ├─────────────┤     ├─────────────┤
│ id (PK)     │◄────┤ tenant_id   │     │ id (PK)     │
│ name        │     │ id (PK)     │◄────┤ owner_id    │
│ subdomain   │     │ email       │     │ tenant_id   │
│ settings    │     │ password    │     │ name        │
│ created_at  │     │ role        │     │ description │
└─────────────┘     │ created_at  │     │ status      │
                    └─────────────┘     │ created_at  │
                                        └─────────────┘
```

### Key Design Decisions

#### UUID Primary Keys
- Prevents ID enumeration attacks
- Enables distributed systems
- Avoids tenant data leakage

#### Tenant Isolation
- Every tenant-specific table includes `tenant_id`
- Foreign key constraints ensure data integrity
- Indexes on `tenant_id` for performance

#### Audit Fields
- `created_at` and `updated_at` timestamps
- Soft deletes with `deleted_at` (where applicable)
- User tracking for audit trails

## Security Architecture

### Authentication & Authorization

#### JWT Token Structure
```json
{
  "userId": "uuid",
  "tenantId": "uuid",
  "role": "admin|user",
  "iat": 1234567890,
  "exp": 1234567890
}
```

#### Role-Based Access Control (RBAC)
- **Admin**: Full access within tenant
- **User**: Limited access within tenant
- **Cross-tenant isolation**: Enforced at middleware level

### Security Measures

#### Input Validation
- Joi schemas for request validation
- SQL injection prevention with parameterized queries
- XSS protection with input sanitization

#### Rate Limiting
- API endpoint rate limiting
- Login attempt rate limiting
- Per-tenant rate limiting

#### Security Headers
- CORS configuration
- Helmet.js security headers
- HTTPS enforcement in production

## API Design

### RESTful API Principles

#### Resource Naming
```
GET    /api/projects           # List projects
POST   /api/projects           # Create project
GET    /api/projects/:id       # Get project
PUT    /api/projects/:id       # Update project
DELETE /api/projects/:id       # Delete project
```

#### Response Format
```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Operation successful",
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

#### Error Format
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": { /* additional error details */ }
}
```

### Tenant Context

#### Automatic Tenant Filtering
All API endpoints automatically filter data by tenant context:

```typescript
// Middleware adds tenant to request
interface TenantRequest extends Request {
  tenant: { id: string; name: string };
}

// Services automatically filter by tenant
const projects = await Project.findAll({
  where: { tenant_id: req.tenant.id }
});
```

### Pagination & Filtering

#### Query Parameters
```
GET /api/projects?page=1&limit=10&search=term&status=active&sortBy=name&sortOrder=ASC
```

#### Response Metadata
```json
{
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

## Performance Considerations

### Database Optimization
- Indexes on frequently queried columns
- Connection pooling
- Query optimization

### Caching Strategy
- React Query for client-side caching
- Redis for server-side caching (future enhancement)
- CDN for static assets

### Scalability
- Horizontal scaling with load balancers
- Database read replicas
- Microservices architecture (future enhancement)

## Future Enhancements

### Planned Features
1. **Advanced Multi-tenancy**
   - Per-tenant databases
   - Custom domains
   - White-label solutions

2. **Enhanced Security**
   - Two-factor authentication
   - SSO integration
   - Advanced audit logging

3. **Performance Improvements**
   - Redis caching
   - Database sharding
   - CDN integration

4. **Monitoring & Analytics**
   - Application performance monitoring
   - User analytics
   - Business intelligence dashboard
