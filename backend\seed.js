const { Sequelize, DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');

// Create database connection
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: './database.sqlite',
  logging: false,
});

// Define models
const Tenant = sequelize.define('Tenant', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  subdomain: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  },
  settings: {
    type: DataTypes.JSON,
    defaultValue: {},
  },
}, {
  tableName: 'tenants',
  timestamps: true,
});

const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  firstName: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  lastName: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  role: {
    type: DataTypes.ENUM('admin', 'user'),
    defaultValue: 'user',
  },
  tenantId: {
    type: DataTypes.UUID,
    allowNull: false,
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  },
  lastLoginAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
}, {
  tableName: 'users',
  timestamps: true,
});

const Project = sequelize.define('Project', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'completed'),
    defaultValue: 'active',
  },
  tenantId: {
    type: DataTypes.UUID,
    allowNull: false,
  },
  ownerId: {
    type: DataTypes.UUID,
    allowNull: false,
  },
  settings: {
    type: DataTypes.JSON,
    defaultValue: {},
  },
}, {
  tableName: 'projects',
  timestamps: true,
});

async function seedDatabase() {
  try {
    console.log('🌱 Seeding database...');

    // Clear existing data
    await Project.destroy({ where: {} });
    await User.destroy({ where: {} });
    await Tenant.destroy({ where: {} });
    console.log('🧹 Cleared existing data');

    // Create multiple tenants
    const defaultTenant = await Tenant.create({
      name: 'Default Organization',
      subdomain: 'default',
      isActive: true,
      settings: { theme: 'blue' },
    });
    console.log('✅ Created default tenant');

    const demoTenant = await Tenant.create({
      name: 'Demo Company Inc',
      subdomain: 'demo',
      isActive: true,
      settings: { theme: 'green', features: ['projects', 'users'] },
    });
    console.log('✅ Created demo tenant');

    const acmeTenant = await Tenant.create({
      name: 'Acme Corporation',
      subdomain: 'acme',
      isActive: true,
      settings: { theme: 'purple', features: ['projects', 'users', 'analytics'] },
    });
    console.log('✅ Created acme tenant');

    const techTenant = await Tenant.create({
      name: 'TechStart Solutions',
      subdomain: 'techstart',
      isActive: true,
      settings: { theme: 'orange', features: ['projects', 'users', 'reports'] },
    });
    console.log('✅ Created techstart tenant');

    // Hash passwords
    const adminPassword = await bcrypt.hash('admin123', 12);
    const userPassword = await bcrypt.hash('user123', 12);

    // Create users for default tenant
    const defaultAdmin = await User.create({
      email: '<EMAIL>',
      password: adminPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: 'admin',
      tenantId: defaultTenant.id,
      isActive: true,
    });
    console.log('✅ Created admin user for default tenant');

    const defaultUser = await User.create({
      email: '<EMAIL>',
      password: userPassword,
      firstName: 'John',
      lastName: 'Doe',
      role: 'user',
      tenantId: defaultTenant.id,
      isActive: true,
    });
    console.log('✅ Created regular user for default tenant');

    // Create users for demo tenant
    const demoAdmin = await User.create({
      email: '<EMAIL>',
      password: adminPassword,
      firstName: 'Demo',
      lastName: 'Admin',
      role: 'admin',
      tenantId: demoTenant.id,
      isActive: true,
    });
    console.log('✅ Created admin user for demo tenant');

    const demoUser = await User.create({
      email: '<EMAIL>',
      password: userPassword,
      firstName: 'Demo',
      lastName: 'User',
      role: 'user',
      tenantId: demoTenant.id,
      isActive: true,
    });
    console.log('✅ Created demo user for demo tenant');

    // Create users for acme tenant
    const acmeAdmin = await User.create({
      email: '<EMAIL>',
      password: adminPassword,
      firstName: 'Alice',
      lastName: 'Johnson',
      role: 'admin',
      tenantId: acmeTenant.id,
      isActive: true,
    });
    console.log('✅ Created admin user for acme tenant');

    const acmeUser = await User.create({
      email: '<EMAIL>',
      password: userPassword,
      firstName: 'Bob',
      lastName: 'Smith',
      role: 'user',
      tenantId: acmeTenant.id,
      isActive: true,
    });
    console.log('✅ Created user for acme tenant');

    // Create users for techstart tenant
    const techAdmin = await User.create({
      email: '<EMAIL>',
      password: adminPassword,
      firstName: 'Sarah',
      lastName: 'Wilson',
      role: 'admin',
      tenantId: techTenant.id,
      isActive: true,
    });
    console.log('✅ Created admin user for techstart tenant');

    const techUser = await User.create({
      email: '<EMAIL>',
      password: userPassword,
      firstName: 'Mike',
      lastName: 'Chen',
      role: 'user',
      tenantId: techTenant.id,
      isActive: true,
    });
    console.log('✅ Created user for techstart tenant');

    // Create projects for default tenant
    await Project.create({
      name: 'Website Redesign',
      description: 'Complete redesign of company website',
      status: 'active',
      tenantId: defaultTenant.id,
      ownerId: defaultAdmin.id,
      settings: { priority: 'high', category: 'web development' },
    });

    await Project.create({
      name: 'Mobile App Development',
      description: 'Native mobile app for iOS and Android',
      status: 'active',
      tenantId: defaultTenant.id,
      ownerId: defaultUser.id,
      settings: { priority: 'medium', category: 'mobile development' },
    });

    // Create projects for demo tenant
    await Project.create({
      name: 'E-commerce Platform',
      description: 'Building a modern e-commerce solution',
      status: 'active',
      tenantId: demoTenant.id,
      ownerId: demoAdmin.id,
      settings: { priority: 'high', category: 'e-commerce' },
    });

    await Project.create({
      name: 'Marketing Campaign',
      description: 'Q4 marketing campaign planning and execution',
      status: 'completed',
      tenantId: demoTenant.id,
      ownerId: demoUser.id,
      settings: { priority: 'medium', category: 'marketing' },
    });

    // Create projects for acme tenant
    await Project.create({
      name: 'Data Analytics Dashboard',
      description: 'Real-time analytics dashboard for business intelligence',
      status: 'active',
      tenantId: acmeTenant.id,
      ownerId: acmeAdmin.id,
      settings: { priority: 'high', category: 'analytics' },
    });

    await Project.create({
      name: 'Customer Support Portal',
      description: 'Self-service portal for customer support',
      status: 'inactive',
      tenantId: acmeTenant.id,
      ownerId: acmeUser.id,
      settings: { priority: 'low', category: 'support' },
    });

    // Create projects for techstart tenant
    await Project.create({
      name: 'AI Chatbot Integration',
      description: 'Integrate AI-powered chatbot for customer service',
      status: 'active',
      tenantId: techTenant.id,
      ownerId: techAdmin.id,
      settings: { priority: 'high', category: 'AI/ML' },
    });

    await Project.create({
      name: 'Cloud Migration',
      description: 'Migrate legacy systems to cloud infrastructure',
      status: 'completed',
      tenantId: techTenant.id,
      ownerId: techUser.id,
      settings: { priority: 'high', category: 'infrastructure' },
    });

    console.log('✅ Created sample projects for all tenants');

    console.log('🎉 Database seeded successfully!');
    console.log('\n📊 Database Summary:');
    console.log('Database Type: SQLite');
    console.log('Location: ./backend/database.sqlite');
    console.log('Tables: tenants, users, projects');
    console.log('\n🏢 Tenants Created: 4');
    console.log('- default (Default Organization)');
    console.log('- demo (Demo Company Inc)');
    console.log('- acme (Acme Corporation)');
    console.log('- techstart (TechStart Solutions)');
    console.log('\n👥 Users Created: 8 (2 per tenant)');
    console.log('📁 Projects Created: 8 (2 per tenant)');
    console.log('\n🔐 Demo Credentials:');
    console.log('=== DEFAULT TENANT ===');
    console.log('Admin: <EMAIL> / admin123');
    console.log('User:  <EMAIL> / user123');
    console.log('\n=== DEMO TENANT ===');
    console.log('Admin: <EMAIL> / admin123');
    console.log('User:  <EMAIL> / user123');
    console.log('\n=== ACME TENANT ===');
    console.log('Admin: <EMAIL> / admin123');
    console.log('User:  <EMAIL> / user123');
    console.log('\n=== TECHSTART TENANT ===');
    console.log('Admin: <EMAIL> / admin123');
    console.log('User:  <EMAIL> / user123');
    console.log('\n🌐 Access URLs:');
    console.log('http://localhost:3000 (will use default tenant)');
    console.log('Add to hosts file for subdomain testing:');
    console.log('127.0.0.1 default.localhost');
    console.log('127.0.0.1 demo.localhost');
    console.log('127.0.0.1 acme.localhost');
    console.log('127.0.0.1 techstart.localhost');
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
  } finally {
    await sequelize.close();
  }
}

seedDatabase();
