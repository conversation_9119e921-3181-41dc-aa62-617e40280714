import { Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { AuthService } from '../services/AuthService';
import { TenantRequest, ApiResponse } from '../types';

export class AuthController {
  static async login(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      // Validation schema
      const schema = Joi.object({
        email: Joi.string().email().required(),
        password: Joi.string().min(6).required(),
        tenantId: Joi.string().optional(),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
        return;
      }

      // Use tenant from middleware if not provided in body
      const loginData = {
        ...value,
        tenantId: value.tenantId || req.tenant?.id,
      };

      const result = await AuthService.login(loginData);

      // Set HTTP-only cookie for token
      res.cookie('token', result.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      const response: ApiResponse = {
        success: true,
        data: result,
        message: 'Login successful',
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async register(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      // Validation schema
      const schema = Joi.object({
        email: Joi.string().email().required(),
        password: Joi.string().min(6).required(),
        firstName: Joi.string().min(1).max(50).required(),
        lastName: Joi.string().min(1).max(50).required(),
        role: Joi.string().valid('admin', 'user').default('user'),
        tenantId: Joi.string().optional(),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
        return;
      }

      // Use tenant from middleware if not provided in body
      const userData = {
        ...value,
        tenantId: value.tenantId || req.tenant?.id,
      };

      const result = await AuthService.register(userData);

      // Set HTTP-only cookie for token
      res.cookie('token', result.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      const response: ApiResponse = {
        success: true,
        data: result,
        message: 'Registration successful',
      };

      res.status(201).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async logout(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      // Clear the token cookie
      res.clearCookie('token');

      const response: ApiResponse = {
        success: true,
        message: 'Logout successful',
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async getCurrentUser(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const user = await AuthService.getCurrentUser(req.user.id, req.user.tenantId);

      if (!user) {
        res.status(404).json({
          success: false,
          error: 'User not found',
        });
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: user.toJSON(),
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async refreshToken(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const newToken = await AuthService.refreshToken(req.user.id, req.user.tenantId);

      // Set new HTTP-only cookie
      res.cookie('token', newToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      const response: ApiResponse = {
        success: true,
        data: { token: newToken },
        message: 'Token refreshed successfully',
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async changePassword(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      // Validation schema
      const schema = Joi.object({
        currentPassword: Joi.string().required(),
        newPassword: Joi.string().min(6).required(),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
        return;
      }

      const success = await AuthService.changePassword(
        req.user.id,
        req.user.tenantId,
        value.currentPassword,
        value.newPassword
      );

      if (!success) {
        res.status(400).json({
          success: false,
          error: 'Failed to change password',
        });
        return;
      }

      const response: ApiResponse = {
        success: true,
        message: 'Password changed successfully',
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }
}
