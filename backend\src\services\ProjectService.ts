import { Op } from 'sequelize';
import { Project } from '../models/Project';
import { User } from '../models/User';
import { Tenant } from '../models/Tenant';
import { ProjectAttributes, ProjectCreationAttributes, PaginationQuery } from '../types';

export class ProjectService {
  static async findById(id: string, tenantId: string): Promise<Project | null> {
    try {
      return await Project.findOne({
        where: {
          id,
          tenantId,
        },
        include: [
          {
            model: User,
            as: 'owner',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
          {
            model: Tenant,
            as: 'tenant',
            attributes: ['id', 'name', 'subdomain'],
          },
        ],
      });
    } catch (error) {
      console.error('Error finding project by ID:', error);
      throw error;
    }
  }

  static async create(projectData: ProjectCreationAttributes): Promise<Project> {
    try {
      const project = await Project.create(projectData);
      return await this.findById(project.id, project.tenantId) as Project;
    } catch (error) {
      console.error('Error creating project:', error);
      throw error;
    }
  }

  static async update(
    id: string,
    tenantId: string,
    updateData: Partial<ProjectAttributes>
  ): Promise<Project | null> {
    try {
      const [updatedRowsCount] = await Project.update(updateData, {
        where: {
          id,
          tenantId,
        },
        returning: true,
      });

      if (updatedRowsCount === 0) {
        return null;
      }

      return await this.findById(id, tenantId);
    } catch (error) {
      console.error('Error updating project:', error);
      throw error;
    }
  }

  static async delete(id: string, tenantId: string): Promise<boolean> {
    try {
      const deletedRowsCount = await Project.destroy({
        where: {
          id,
          tenantId,
        },
      });

      return deletedRowsCount > 0;
    } catch (error) {
      console.error('Error deleting project:', error);
      throw error;
    }
  }

  static async list(
    tenantId: string,
    options: PaginationQuery & { status?: string; ownerId?: string } = {}
  ): Promise<{ projects: Project[]; total: number }> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createdAt',
        sortOrder = 'DESC',
        status,
        ownerId,
      } = options;
      const offset = (page - 1) * limit;

      const whereClause: any = {
        tenantId,
      };

      if (search) {
        whereClause[Op.or] = [
          { name: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } },
        ];
      }

      if (status) {
        whereClause.status = status;
      }

      if (ownerId) {
        whereClause.ownerId = ownerId;
      }

      const { count, rows } = await Project.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        order: [[sortBy, sortOrder]],
        include: [
          {
            model: User,
            as: 'owner',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
          {
            model: Tenant,
            as: 'tenant',
            attributes: ['id', 'name', 'subdomain'],
          },
        ],
      });

      return {
        projects: rows,
        total: count,
      };
    } catch (error) {
      console.error('Error listing projects:', error);
      throw error;
    }
  }

  static async getProjectsByOwner(
    ownerId: string,
    tenantId: string,
    options: PaginationQuery = {}
  ): Promise<{ projects: Project[]; total: number }> {
    return this.list(tenantId, { ...options, ownerId });
  }

  static async getProjectStats(tenantId: string): Promise<{
    total: number;
    active: number;
    completed: number;
    inactive: number;
  }> {
    try {
      const total = await Project.count({
        where: { tenantId },
      });

      const active = await Project.count({
        where: {
          tenantId,
          status: 'active',
        },
      });

      const completed = await Project.count({
        where: {
          tenantId,
          status: 'completed',
        },
      });

      const inactive = await Project.count({
        where: {
          tenantId,
          status: 'inactive',
        },
      });

      return {
        total,
        active,
        completed,
        inactive,
      };
    } catch (error) {
      console.error('Error getting project stats:', error);
      throw error;
    }
  }

  static async updateStatus(
    id: string,
    tenantId: string,
    status: 'active' | 'inactive' | 'completed'
  ): Promise<Project | null> {
    try {
      return await this.update(id, tenantId, { status });
    } catch (error) {
      console.error('Error updating project status:', error);
      throw error;
    }
  }

  static async transferOwnership(
    id: string,
    tenantId: string,
    newOwnerId: string
  ): Promise<Project | null> {
    try {
      // Verify the new owner exists and belongs to the same tenant
      const newOwner = await User.findOne({
        where: {
          id: newOwnerId,
          tenantId,
          isActive: true,
        },
      });

      if (!newOwner) {
        throw new Error('New owner not found or not active in this tenant');
      }

      return await this.update(id, tenantId, { ownerId: newOwnerId });
    } catch (error) {
      console.error('Error transferring project ownership:', error);
      throw error;
    }
  }
}
