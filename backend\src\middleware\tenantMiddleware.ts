import { Response, NextFunction } from 'express';
import { TenantRequest } from '../types';
import { TenantService } from '../services/TenantService';

export const tenantMiddleware = async (
  req: TenantRequest,
  res: Response,
  next: NextFunction
): Promise<void | Response> => {
  try {
    let tenantIdentifier: string | undefined;

    // Method 1: Extract tenant from subdomain
    const host = req.get('host');
    if (host) {
      // Remove port from host if present
      const hostname = host.split(':')[0];
      const subdomain = hostname.split('.')[0];
      // Check if it's not localhost or an IP address
      if (subdomain && subdomain !== 'localhost' && !hostname.match(/^\d+\.\d+\.\d+\.\d+/)) {
        tenantIdentifier = subdomain;
      }
    }

    // Method 2: Fallback to header-based tenant identification
    if (!tenantIdentifier) {
      tenantIdentifier = req.get(process.env.TENANT_HEADER_NAME || 'X-Tenant-ID');
    }

    // Method 3: Fallback to default tenant for development
    if (!tenantIdentifier && process.env.NODE_ENV === 'development') {
      tenantIdentifier = process.env.DEFAULT_TENANT || 'default';
    }

    if (!tenantIdentifier) {
      return res.status(400).json({
        success: false,
        error: 'Tenant identification required. Use subdomain or X-Tenant-ID header.',
      });
    }

    // Fetch tenant information
    const tenant = await TenantService.findByIdentifier(tenantIdentifier);
    
    if (!tenant) {
      return res.status(404).json({
        success: false,
        error: 'Tenant not found or inactive.',
      });
    }

    // Attach tenant information to request
    req.tenant = {
      id: tenant.id,
      name: tenant.name,
      subdomain: tenant.subdomain,
    };

    next();
  } catch (error) {
    console.error('Tenant middleware error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during tenant identification.',
    });
  }
};
