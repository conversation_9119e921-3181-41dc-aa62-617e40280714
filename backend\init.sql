-- Initialize database for multi-tenant application
-- This script creates the database and initial data

-- <PERSON><PERSON> database if it doesn't exist
-- Note: This is handled by <PERSON><PERSON>, but included for reference

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create initial tenant for development
INSERT INTO tenants (id, name, subdomain, "isActive", settings, "createdAt", "updatedAt")
VALUES (
  uuid_generate_v4(),
  'Default Tenant',
  'default',
  true,
  '{}',
  NOW(),
  NOW()
) ON CONFLICT (subdomain) DO NOTHING;

-- Create demo tenant
INSERT INTO tenants (id, name, subdomain, "isActive", settings, "createdAt", "updatedAt")
VALUES (
  uuid_generate_v4(),
  'Demo Company',
  'demo',
  true,
  '{"theme": "blue", "features": ["projects", "users"]}',
  NOW(),
  NOW()
) ON CONFLICT (subdomain) DO NOTHING;

-- Create admin user for default tenant
-- Password: admin123 (hashed with bcrypt)
INSERT INTO users (
  id, 
  email, 
  password, 
  "firstName", 
  "lastName", 
  role, 
  "tenantId", 
  "isActive", 
  "createdAt", 
  "updatedAt"
)
SELECT 
  uuid_generate_v4(),
  '<EMAIL>',
  '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL.ckstG.',
  'Admin',
  'User',
  'admin',
  t.id,
  true,
  NOW(),
  NOW()
FROM tenants t 
WHERE t.subdomain = 'default'
ON CONFLICT (email, "tenantId") DO NOTHING;

-- Create demo user for demo tenant
-- Password: demo123 (hashed with bcrypt)
INSERT INTO users (
  id, 
  email, 
  password, 
  "firstName", 
  "lastName", 
  role, 
  "tenantId", 
  "isActive", 
  "createdAt", 
  "updatedAt"
)
SELECT 
  uuid_generate_v4(),
  '<EMAIL>',
  '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL.ckstG.',
  'Demo',
  'User',
  'user',
  t.id,
  true,
  NOW(),
  NOW()
FROM tenants t 
WHERE t.subdomain = 'demo'
ON CONFLICT (email, "tenantId") DO NOTHING;

-- Create sample projects for demo tenant
INSERT INTO projects (
  id,
  name,
  description,
  status,
  "tenantId",
  "ownerId",
  settings,
  "createdAt",
  "updatedAt"
)
SELECT 
  uuid_generate_v4(),
  'Sample Project 1',
  'This is a sample project for demonstration purposes',
  'active',
  t.id,
  u.id,
  '{"priority": "high", "category": "development"}',
  NOW(),
  NOW()
FROM tenants t
JOIN users u ON u."tenantId" = t.id
WHERE t.subdomain = 'demo' AND u.email = '<EMAIL>'
ON CONFLICT DO NOTHING;

INSERT INTO projects (
  id,
  name,
  description,
  status,
  "tenantId",
  "ownerId",
  settings,
  "createdAt",
  "updatedAt"
)
SELECT 
  uuid_generate_v4(),
  'Sample Project 2',
  'Another sample project with completed status',
  'completed',
  t.id,
  u.id,
  '{"priority": "medium", "category": "design"}',
  NOW(),
  NOW()
FROM tenants t
JOIN users u ON u."tenantId" = t.id
WHERE t.subdomain = 'demo' AND u.email = '<EMAIL>'
ON CONFLICT DO NOTHING;
