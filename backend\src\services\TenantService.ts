import { Op } from 'sequelize';
import { Tenant } from '../models/Tenant';
import { TenantAttributes, TenantCreationAttributes } from '../types';

export class TenantService {
  static async findByIdentifier(identifier: string): Promise<Tenant | null> {
    try {
      // Try to find by subdomain first
      let tenant = await Tenant.findOne({
        where: {
          subdomain: identifier,
          isActive: true,
        },
      });

      // If not found by subdomain, try by ID
      if (!tenant) {
        tenant = await Tenant.findOne({
          where: {
            id: identifier,
            isActive: true,
          },
        });
      }

      return tenant;
    } catch (error) {
      console.error('Error finding tenant:', error);
      throw error;
    }
  }

  static async findById(id: string): Promise<Tenant | null> {
    try {
      return await Tenant.findOne({
        where: {
          id,
          isActive: true,
        },
      });
    } catch (error) {
      console.error('Error finding tenant by ID:', error);
      throw error;
    }
  }

  static async findBySubdomain(subdomain: string): Promise<Tenant | null> {
    try {
      return await Tenant.findOne({
        where: {
          subdomain,
          isActive: true,
        },
      });
    } catch (error) {
      console.error('Error finding tenant by subdomain:', error);
      throw error;
    }
  }

  static async create(tenantData: TenantCreationAttributes): Promise<Tenant> {
    try {
      return await Tenant.create(tenantData);
    } catch (error) {
      console.error('Error creating tenant:', error);
      throw error;
    }
  }

  static async update(id: string, updateData: Partial<TenantAttributes>): Promise<Tenant | null> {
    try {
      const [updatedRowsCount] = await Tenant.update(updateData, {
        where: { id },
        returning: true,
      });

      if (updatedRowsCount === 0) {
        return null;
      }

      return await this.findById(id);
    } catch (error) {
      console.error('Error updating tenant:', error);
      throw error;
    }
  }

  static async delete(id: string): Promise<boolean> {
    try {
      // Soft delete by setting isActive to false
      const [updatedRowsCount] = await Tenant.update(
        { isActive: false },
        { where: { id } }
      );

      return updatedRowsCount > 0;
    } catch (error) {
      console.error('Error deleting tenant:', error);
      throw error;
    }
  }

  static async list(options: {
    page?: number;
    limit?: number;
    search?: string;
  } = {}): Promise<{ tenants: Tenant[]; total: number }> {
    try {
      const { page = 1, limit = 10, search } = options;
      const offset = (page - 1) * limit;

      const whereClause: any = { isActive: true };

      if (search) {
        whereClause[Op.or] = [
          { name: { [Op.iLike]: `%${search}%` } },
          { subdomain: { [Op.iLike]: `%${search}%` } },
        ];
      }

      const { count, rows } = await Tenant.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        order: [['createdAt', 'DESC']],
      });

      return {
        tenants: rows,
        total: count,
      };
    } catch (error) {
      console.error('Error listing tenants:', error);
      throw error;
    }
  }
}
