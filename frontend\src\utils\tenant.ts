/**
 * Utility functions for tenant management
 */

export const getTenantFromSubdomain = (): string | null => {
  const hostname = window.location.hostname;
  
  // Skip localhost and IP addresses
  if (hostname === 'localhost' || hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
    return null;
  }
  
  // Extract subdomain
  const parts = hostname.split('.');
  if (parts.length > 2) {
    return parts[0];
  }
  
  return null;
};

export const getTenantFromPath = (): string | null => {
  const pathname = window.location.pathname;
  const pathParts = pathname.split('/').filter(Boolean);
  
  // Check if first path segment could be a tenant identifier
  if (pathParts.length > 0 && pathParts[0].match(/^[a-z0-9-]+$/)) {
    return pathParts[0];
  }
  
  return null;
};

export const getCurrentTenant = (): string | null => {
  // Try subdomain first
  const subdomainTenant = getTenantFromSubdomain();
  if (subdomainTenant) {
    return subdomainTenant;
  }

  // Fallback to path-based tenant
  const pathTenant = getTenantFromPath();
  if (pathTenant) {
    return pathTenant;
  }

  // Always fallback to default tenant for localhost
  return import.meta.env.VITE_DEFAULT_TENANT || 'default';
};

export const buildTenantUrl = (tenant: string, path: string = ''): string => {
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  const port = window.location.port;
  
  // For localhost, use path-based routing
  if (hostname === 'localhost' || hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
    const portPart = port ? `:${port}` : '';
    return `${protocol}//${hostname}${portPart}/${tenant}${path}`;
  }
  
  // For production, use subdomain-based routing
  const baseDomain = hostname.split('.').slice(-2).join('.');
  const portPart = port ? `:${port}` : '';
  return `${protocol}//${tenant}.${baseDomain}${portPart}${path}`;
};

export const isTenantContext = (): boolean => {
  return getCurrentTenant() !== null;
};

export const redirectToTenant = (tenant: string, path: string = '') => {
  const url = buildTenantUrl(tenant, path);
  window.location.href = url;
};
