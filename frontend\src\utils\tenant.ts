/**
 * Utility functions for tenant management
 */

export const getTenantFromSubdomain = (): string | null => {
  const hostname = window.location.hostname;
  
  // Skip localhost and IP addresses
  if (hostname === 'localhost' || hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
    return null;
  }
  
  // Extract subdomain
  const parts = hostname.split('.');
  if (parts.length > 2) {
    return parts[0];
  }
  
  return null;
};

export const getTenantFromPath = (): string | null => {
  const pathname = window.location.pathname;
  const pathParts = pathname.split('/').filter(Boolean);

  // List of common app routes that should not be treated as tenant identifiers
  const excludedPaths = [
    'login', 'register', 'logout', 'dashboard', 'projects', 'users', 'profile',
    'settings', 'admin', 'api', 'health', 'tenant-not-found', 'error'
  ];

  // Check if first path segment could be a tenant identifier
  if (pathParts.length > 0 &&
      pathParts[0].match(/^[a-z0-9-]+$/) &&
      !excludedPaths.includes(pathParts[0])) {
    return pathParts[0];
  }

  return null;
};

export const getCurrentTenant = (): string | null => {
  // For localhost development, always use the default tenant
  if (window.location.hostname === 'localhost' || window.location.hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
    return import.meta.env.VITE_DEFAULT_TENANT || 'lshack';
  }

  // Try subdomain first for production
  const subdomainTenant = getTenantFromSubdomain();
  if (subdomainTenant) {
    return subdomainTenant;
  }

  // Fallback to path-based tenant (rarely used)
  const pathTenant = getTenantFromPath();
  if (pathTenant) {
    return pathTenant;
  }

  // Final fallback
  return import.meta.env.VITE_DEFAULT_TENANT || 'lshack';
};

export const buildTenantUrl = (tenant: string, path: string = ''): string => {
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  const port = window.location.port;
  
  // For localhost, use path-based routing
  if (hostname === 'localhost' || hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
    const portPart = port ? `:${port}` : '';
    return `${protocol}//${hostname}${portPart}/${tenant}${path}`;
  }
  
  // For production, use subdomain-based routing
  const baseDomain = hostname.split('.').slice(-2).join('.');
  const portPart = port ? `:${port}` : '';
  return `${protocol}//${tenant}.${baseDomain}${portPart}${path}`;
};

export const isTenantContext = (): boolean => {
  return getCurrentTenant() !== null;
};

export const redirectToTenant = (tenant: string, path: string = '') => {
  const url = buildTenantUrl(tenant, path);
  window.location.href = url;
};
