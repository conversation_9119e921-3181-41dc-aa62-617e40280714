import { Request } from 'express';

// Extend Express Request interface to include tenant information
export interface TenantRequest extends Request {
  tenant?: {
    id: string;
    name: string;
    subdomain?: string;
  };
  user?: {
    id: string;
    email: string;
    tenantId: string;
    role: string;
  };
}

// User types
export interface UserAttributes {
  id: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'user';
  tenantId: string;
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserCreationAttributes extends Omit<UserAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// Tenant types
export interface TenantAttributes {
  id: string;
  name: string;
  subdomain: string;
  isActive: boolean;
  settings: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface TenantCreationAttributes extends Omit<TenantAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// Project types
export interface ProjectAttributes {
  id: string;
  name: string;
  description?: string;
  status: 'active' | 'inactive' | 'completed';
  tenantId: string;
  ownerId: string;
  settings: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectCreationAttributes extends Omit<ProjectAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
  };
}

// Authentication types
export interface LoginRequest {
  email: string;
  password: string;
  tenantId?: string;
}

export interface LoginResponse {
  user: Omit<UserAttributes, 'password'>;
  token: string;
  tenant: TenantAttributes;
}

// Pagination types
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  search?: string;
}

// Error types
export interface AppError extends Error {
  statusCode: number;
  isOperational: boolean;
}
