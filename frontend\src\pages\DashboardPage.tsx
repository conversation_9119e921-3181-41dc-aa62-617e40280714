import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { 
  FolderOpen, 
  Users, 
  Activity, 
  TrendingUp,
  Plus,
  ArrowRight
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { projectService } from '@/services/projects';
import { userService } from '@/services/users';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

export const DashboardPage: React.FC = () => {
  const { user } = useAuth();

  // Fetch project statistics
  const { data: projectStats, isLoading: projectStatsLoading } = useQuery({
    queryKey: ['projectStats'],
    queryFn: projectService.getProjectStats,
  });

  // Fetch user statistics (admin only)
  const { data: userStats, isLoading: userStatsLoading } = useQuery({
    queryKey: ['userStats'],
    queryFn: userService.getUserStats,
    enabled: user?.role === 'admin',
  });

  // Fetch recent projects
  const { data: recentProjects, isLoading: recentProjectsLoading } = useQuery({
    queryKey: ['recentProjects'],
    queryFn: () => projectService.getMyProjects({ limit: 5, sortBy: 'updatedAt' }),
  });

  const stats = [
    {
      name: 'Total Projects',
      value: projectStats?.total || 0,
      icon: FolderOpen,
      color: 'bg-blue-500',
      loading: projectStatsLoading,
    },
    {
      name: 'Active Projects',
      value: projectStats?.active || 0,
      icon: Activity,
      color: 'bg-green-500',
      loading: projectStatsLoading,
    },
    ...(user?.role === 'admin' ? [
      {
        name: 'Total Users',
        value: userStats?.total || 0,
        icon: Users,
        color: 'bg-purple-500',
        loading: userStatsLoading,
      },
      {
        name: 'Active Users',
        value: userStats?.active || 0,
        icon: TrendingUp,
        color: 'bg-orange-500',
        loading: userStatsLoading,
      },
    ] : []),
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">
            Welcome back, {user?.firstName}! Here's what's happening.
          </p>
        </div>
        <Link to="/projects">
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            New Project
          </Button>
        </Link>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="card">
              <div className="card-content">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg ${stat.color}`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                    <div className="flex items-center">
                      {stat.loading ? (
                        <LoadingSpinner size="sm" />
                      ) : (
                        <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Recent Projects */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <h3 className="card-title">Recent Projects</h3>
            <Link 
              to="/projects" 
              className="text-sm text-primary-600 hover:text-primary-700 flex items-center"
            >
              View all
              <ArrowRight className="w-4 h-4 ml-1" />
            </Link>
          </div>
        </div>
        <div className="card-content">
          {recentProjectsLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : recentProjects?.projects.length === 0 ? (
            <div className="text-center py-8">
              <FolderOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No projects yet</h3>
              <p className="text-gray-600 mb-4">Get started by creating your first project.</p>
              <Link to="/projects">
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Project
                </Button>
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {recentProjects?.projects.map((project) => (
                <div key={project.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                      <FolderOpen className="w-5 h-5 text-primary-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{project.name}</h4>
                      <p className="text-sm text-gray-600">{project.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className={`badge ${
                      project.status === 'active' ? 'badge-success' :
                      project.status === 'completed' ? 'badge-secondary' :
                      'badge-warning'
                    }`}>
                      {project.status}
                    </span>
                    <Link 
                      to={`/projects/${project.id}`}
                      className="text-primary-600 hover:text-primary-700"
                    >
                      <ArrowRight className="w-4 h-4" />
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
