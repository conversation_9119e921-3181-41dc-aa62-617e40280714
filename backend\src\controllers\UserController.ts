import { Response, NextFunction } from 'express';
import Jo<PERSON> from 'joi';
import { UserService } from '../services/UserService';
import { TenantRequest, ApiResponse } from '../types';

export class UserController {
  static async getUsers(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant) {
        res.status(400).json({
          success: false,
          error: 'Tenant context required',
        });
        return;
      }

      // Validation schema for query parameters
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1),
        limit: Joi.number().integer().min(1).max(100).default(10),
        search: Joi.string().optional(),
        sortBy: Joi.string().valid('createdAt', 'firstName', 'lastName', 'email', 'lastLoginAt').default('createdAt'),
        sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC'),
      });

      const { error, value } = schema.validate(req.query);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
        return;
      }

      const result = await UserService.list(req.tenant.id, value);

      const response: ApiResponse = {
        success: true,
        data: result.users,
        meta: {
          page: value.page,
          limit: value.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / value.limit),
        },
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async getUserById(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant) {
        res.status(400).json({
          success: false,
          error: 'Tenant context required',
        });
        return;
      }

      const { id } = req.params;

      const user = await UserService.findById(id, req.tenant.id);

      if (!user) {
        res.status(404).json({
          success: false,
          error: 'User not found',
        });
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: user.toJSON(),
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async createUser(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant) {
        res.status(400).json({
          success: false,
          error: 'Tenant context required',
        });
        return;
      }

      // Validation schema
      const schema = Joi.object({
        email: Joi.string().email().required(),
        password: Joi.string().min(6).required(),
        firstName: Joi.string().min(1).max(50).required(),
        lastName: Joi.string().min(1).max(50).required(),
        role: Joi.string().valid('admin', 'user').default('user'),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
        return;
      }

      // Check if user already exists
      const existingUser = await UserService.findByEmail(value.email, req.tenant.id);
      if (existingUser) {
        res.status(409).json({
          success: false,
          error: 'User already exists with this email',
        });
        return;
      }

      const userData = {
        ...value,
        tenantId: req.tenant.id,
        isActive: true,
      };

      const user = await UserService.create(userData);

      const response: ApiResponse = {
        success: true,
        data: user.toJSON(),
        message: 'User created successfully',
      };

      res.status(201).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async updateUser(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant) {
        res.status(400).json({
          success: false,
          error: 'Tenant context required',
        });
        return;
      }

      const { id } = req.params;

      // Validation schema
      const schema = Joi.object({
        firstName: Joi.string().min(1).max(50).optional(),
        lastName: Joi.string().min(1).max(50).optional(),
        role: Joi.string().valid('admin', 'user').optional(),
        isActive: Joi.boolean().optional(),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
        return;
      }

      const user = await UserService.update(id, req.tenant.id, value);

      if (!user) {
        res.status(404).json({
          success: false,
          error: 'User not found',
        });
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: user.toJSON(),
        message: 'User updated successfully',
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async deleteUser(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant) {
        res.status(400).json({
          success: false,
          error: 'Tenant context required',
        });
        return;
      }

      const { id } = req.params;

      // Prevent users from deleting themselves
      if (req.user && req.user.id === id) {
        res.status(400).json({
          success: false,
          error: 'Cannot delete your own account',
        });
        return;
      }

      const success = await UserService.delete(id, req.tenant.id);

      if (!success) {
        res.status(404).json({
          success: false,
          error: 'User not found',
        });
        return;
      }

      const response: ApiResponse = {
        success: true,
        message: 'User deleted successfully',
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  static async getUserStats(req: TenantRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.tenant) {
        res.status(400).json({
          success: false,
          error: 'Tenant context required',
        });
        return;
      }

      const stats = await UserService.getUserStats(req.tenant.id);

      const response: ApiResponse = {
        success: true,
        data: stats,
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }
}
